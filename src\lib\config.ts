import type { AppSettings, ApiKeys, ProviderSpecificModels, LastCustomModels, LastSelectedModels } from './types';

// Model configuration interface
export interface ModelConfig {
    id: string;
    name: string;
}

// Application constants
export const AUTOCOMPLETE_CONTEXT_LENGTH = 1000;
export const API_TIMEOUT = 30000; // 30 seconds
export const OPENROUTER_HEADERS = {
    'HTTP-Referer': 'https://ai-notepad-app.local',
    'X-Title': 'AI Notepad'
};

// Available models for each provider
export const AVAILABLE_MODELS: Record<string, ModelConfig[]> = {
    gemini: [
        { id: "gemini-2.5-flash", name: "Gemini 2.5 Flash - Very Fast, Thinking Capable" },
        { id: "gemini-2.5-pro", name: "Gemini 2.5 Pro - Powerful, Thinking Capable" },
        { id: "gemini-2.5-flash-lite-preview-06-17", name: "Gemini 2.5 Flash Lite (Preview) - Lightweight, Thinking Capable" },
        { id: "gemini-2.0-flash", name: "Gemini 2.0 Flash - Fast, Balanced" },
        { id: "gemini-2.0-flash-lite", name: "Gemini 2.0 Flash Lite - Very Fast, Lightweight" },
        { id: "gemini-1.5-flash", name: "Gemini 1.5 Flash - Fast, Efficient, Good Value" },
        { id: "gemini-1.5-flash-8b", name: "Gemini 1.5 Flash (8B) - Compact & Fast" },
        { id: "gemini-1.5-pro", name: "Gemini 1.5 Pro - Advanced, Large Context" },
    ],
    openai: [
        { id: "gpt-4o-mini", name: "GPT-4o mini - Fast, Cost-Effective, Multimodal" },
        { id: "gpt-4o", name: "GPT-4o - Flagship, Intelligent, Multimodal" },
        { id: "gpt-3.5-turbo", name: "GPT-3.5 Turbo - Fast, Affordable, Text-Focused" },
    ],
    custom: [] // Custom models are entered by user
};

// Helper function to detect system dark mode preference
function getSystemDarkModePreference(): boolean {
    if (typeof window !== 'undefined' && window.matchMedia) {
        try {
            return window.matchMedia('(prefers-color-scheme: dark)').matches;
        } catch (e) {
            // Fallback if matchMedia fails
            return false;
        }
    }
    return false;
}

// Default system prompts for AI features
export const DEFAULT_SYSTEM_PROMPTS = {
    autocomplete: "Continue writing the following text in the same style and tone. IMPORTANT: Respect all whitespace in the provided text exactly as it appears, including any trailing spaces. If the text ends with a space, start your continuation appropriately without adding extra spaces. Only provide the continuation, do not repeat the existing text:",
    replaceSelection: "Provide text that would naturally fit between the following two text segments. Write in the same style and tone as the surrounding text. IMPORTANT: Respect all whitespace in the provided text exactly as it appears, including any trailing or leading spaces. Your response should connect naturally with the existing whitespace. Only provide the connecting text, nothing else.",
    insertAtCursor: "Provide text that would naturally fit at the cursor position between the following two text segments. Write in the same style and tone as the surrounding text. IMPORTANT: Respect all whitespace in the provided text exactly as it appears, including any trailing or leading spaces. Your response should connect naturally with the existing whitespace. Only provide the text to insert, nothing else."
};

// Default application settings
export const DEFAULT_APP_SETTINGS: AppSettings = {
    fontFamily: 'Arial, sans-serif',
    fontSize: '16',
    textAlign: 'left',
    lineSpacing: '1.5',
    aiProvider: 'openai',
    aiModel: AVAILABLE_MODELS.openai[0].id,
    autocompleteContextLength: AUTOCOMPLETE_CONTEXT_LENGTH,
    temperature: 0.7,
    topP: 0.9,
    sidebarVisible: true,
    darkMode: getSystemDarkModePreference(),
    showThinkingPanel: true,
    systemPrompts: DEFAULT_SYSTEM_PROMPTS
};

// Default API keys (empty)
export const DEFAULT_API_KEYS: ApiKeys = {
    gemini: '',
    openai: '',
    customUrl: '',
    customKey: ''
};

// Default provider-specific models
export const DEFAULT_PROVIDER_SPECIFIC_MODELS: ProviderSpecificModels = {
    gemini: AVAILABLE_MODELS.gemini[0].id,
    openai: AVAILABLE_MODELS.openai[0].id,
    custom: ''
};

// Default last custom models
export const DEFAULT_LAST_CUSTOM_MODELS: LastCustomModels = {
    gemini: '',
    openai: '',
    custom: ''
};

// Default last selected models
export const DEFAULT_LAST_SELECTED_MODELS: LastSelectedModels = {
    gemini: AVAILABLE_MODELS.gemini[0].id,
    openai: AVAILABLE_MODELS.openai[0].id,
    custom: ''
};

// Storage keys for localStorage
export const STORAGE_KEYS = {
    NOTEPAD_CONTENT: 'aiNotepadSvelteContent',
    API_KEYS: 'aiNotepadSvelteApiKeys',
    APP_SETTINGS: 'aiNotepadSvelteSettings',
    CUSTOM_MODEL_HISTORY: 'aiNotepadSvelteCustomModelHistory',
    PROVIDER_SPECIFIC_MODELS: 'aiNotepadSvelteProviderSpecificModels'
} as const;
